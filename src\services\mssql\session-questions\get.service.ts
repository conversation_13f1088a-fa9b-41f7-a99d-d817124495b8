import mssql from '@lcs/mssql-utility'
import { SessionQuestionModel } from '../../../models/session-question.model.js'
import { EvalSessionQuestion, SessionQuestionsTableName, SessionQuestionFields } from '@tess-f/sql-tables/dist/evaluations/session-questions.js'
import { EvaluationQuestionFields, EvaluationQuestionTableName } from '@tess-f/sql-tables/dist/evaluations/evaluation-questions.js'
import { EvaluationSectionVersionFields, EvaluationSectionVersionTableName } from '@tess-f/sql-tables/dist/evaluations/section-version.js'
import { SessionsTableName, SessionFields } from '@tess-f/sql-tables/dist/evaluations/session.js'

// Extended type to include section information
type SessionQuestionWithSection = EvalSessionQuestion & {
  SectionId?: string
  SectionVersion?: number
  SectionDisplayIndex?: number
  DisplayIndexWithinSection?: number
  SectionTitle?: string
}
/** 
* Get all session questions for a specific session, ordered by presentation index.
* Includes section information by joining with the evaluation questions and section tables.
*/
export default async function getSessionQuestions (sessionId: string): Promise<SessionQuestionModel[]> {
  const request = mssql.getPool().request()
  request.input('sessionId', sessionId)
  const results = await request.query<SessionQuestionWithSection>(`
    SELECT
      sq.[${SessionQuestionFields.SessionId}],
      sq.[${SessionQuestionFields.QuestionId}],
      sq.[${SessionQuestionFields.QuestionVersion}],
      sq.[${SessionQuestionFields.PresentationIndex}],
      sq.[${SessionQuestionFields.QuestionSetId}],
      sq.[${SessionQuestionFields.QuestionSetVersion}],
      sv.[${EvaluationSectionVersionFields.SectionId}] as SectionId,
      sv.[${EvaluationSectionVersionFields.Version}] as SectionVersion,
      sv.[${EvaluationSectionVersionFields.DisplayIndex}] as SectionDisplayIndex,
      eq.[${EvaluationQuestionFields.DisplayIndex}] as DisplayIndexWithinSection,
      sv.[${EvaluationSectionVersionFields.Title}] as SectionTitle
    FROM [${SessionQuestionsTableName}] sq
    INNER JOIN [${SessionsTableName}] s ON s.[${SessionFields.Id}] = sq.[${SessionQuestionFields.SessionId}]
    INNER JOIN [${EvaluationQuestionTableName}] eq ON
      eq.[${EvaluationQuestionFields.EvaluationId}] = s.[${SessionFields.EvalId}]
      AND eq.[${EvaluationQuestionFields.EvaluationVersion}] = s.[${SessionFields.EvalVersion}]
      AND (
        (eq.[${EvaluationQuestionFields.QuestionId}] = sq.[${SessionQuestionFields.QuestionId}]
         AND eq.[${EvaluationQuestionFields.QuestionVersion}] = sq.[${SessionQuestionFields.QuestionVersion}])
        OR
        (eq.[${EvaluationQuestionFields.QuestionSetId}] = sq.[${SessionQuestionFields.QuestionSetId}]
         AND eq.[${EvaluationQuestionFields.QuestionSetVersion}] = sq.[${SessionQuestionFields.QuestionSetVersion}])
      )
    INNER JOIN [${EvaluationSectionVersionTableName}] sv ON
      sv.[${EvaluationSectionVersionFields.SectionId}] = eq.[${EvaluationQuestionFields.SectionId}]
      AND sv.[${EvaluationSectionVersionFields.Version}] = eq.[${EvaluationQuestionFields.SectionVersion}]
    WHERE sq.[${SessionQuestionFields.SessionId}] = @sessionId
    ORDER BY sq.[${SessionQuestionFields.PresentationIndex}] ASC
  `)

  return results.recordset.map(record => new SessionQuestionModel(undefined, record))
}
