# Evaluations Engine

This API houses the need resources for TESS applications to communicate with the evaluation module.

## Resources

### Get Evaluation

The endpoint `GET evaluation/:id` will return information about the requested evaluation along with a list of questions for the evaluation. The question list may or may not include all the questions associated with this evaluation. The questions returned are chosen based on the evaluations settings.

For assessment-type evaluations (EvaluationTypeId = 1), a session parameter is required: `GET evaluation/:id/:session`. The system will check if the session already contains assessment data:
- If assessment data exists, it will load and return the existing assessment for viewing
- If no assessment data exists, it will generate a new assessment and save it to the session

### Initialize Assessment Session

The endpoint `POST evaluation/init/:id` is used to initialize a session for launching an assessment. The endpoint requires that you pass in the body a user id. The endpoint will respond back with a unique session id, this id will be active for 5 minutes after this point a new session will need to be generated if the session has not been redeemed. The session can only be redeemed one time.

<details>
  <summary>Request Body</summary>

  ```json
  {
    "userId": "user-id-guid"
  }
  ```
</details>

### Get Assessment Session Data

The endpoint `GET evaluation/session-data/:id` will return back the session data needed to initialize the assessment client. This endpoint retrieves the session record from the database.

<details>
  <summary>Response Body</summary>

  ```json
  {
    "Id": "database-session-id-guid",
    "UserId": "user-id-guid",
    "EvalId": "evaluation-id-guid",
    "EvalVersion": 1,
    "Start": "2024-01-01T00:00:00.000Z",
    "End": null,
    "SubmittedBy": null,
    "Notes": null,
    "Score": null,
    "Passed": null
  }
  ```
</details>

### Submit Assessment

The endpoint `POST evaluation/submit/:id` is used to submit completed assessment responses. The endpoint expects an array of QuestionResponse objects in the request body. The session ID is extracted from the URL path parameter.

<details>
  <summary>Request Body</summary>

  ```json
  [
    {
      "QuestionId": "question-id-guid",
      "QuestionVersion": 1,
      "OptionId": "option-id-guid",
      "OptionVersion": 1,
      "TargetOptionId": "target-option-id-guid",
      "TargetOptionVersion": 1,
      "ResponseText": "Text response for open-ended questions",
      "Duration": "PT30S",
      "OrderId": 1
    }
  ]
  ```
</details>

<details>
  <summary>Response</summary>

  Returns HTTP 204 No Content on successful submission. The assessment is processed asynchronously after the response is sent.
</details>

### Initialize Checklist Session

The endpoint `POST checklist/init/:id` is used to initialize a session for launching a checklist. The endpoint requires that you pass in the body a list of user ids. The endpoint will respond back with a unique session id, this id will be active for 5 minutes after this point a new session will need to be generated if the session has not been redeemed. The session can only be redeemed one time.

<details>
  <summary>Request Body</summary>

  ```json
  {
    "userIds": ["id-1", "id-2"]
  }
  ```
</details>

### Get Checklist Session Data

The endpoint `GET checklist/session-data/:id` will return back information need to initialize the checklist client. This endpoint will return the Id of the checklist to launch and the list of user ids given at session initialization.

<details>
  <summary>Response Body</summary>

  ```json
  {
    "checklistId": "id-of-the-checklist",
    "userIds": ["user-id-1"]
  }
  ```
</details>
