import { Router } from 'express'
import getEvaluationController from './get.controller.js'
import searchController from './search.controller.js'
import initAssessmentSessionController from './init-assessment-session.controller.js'
import getAssessmentSessionDataController from './get-assessment-session-data.controller.js'
import submitAssessmentController from './submit-assessment.controller.js'

// middlewares
import checkSessionMiddleware from '../middlewares/check-session.middleware.js'

const router = Router()

router.post('/search', searchController)

router.use(checkSessionMiddleware)

router.post('/init/:id', initAssessmentSessionController)
router.post('/submit/:id', submitAssessmentController)
router.get('/session-data/:id', getAssessmentSessionDataController)
router.get('/:id', getEvaluationController)
router.get('/:id/:session', getEvaluationController)

export default router
