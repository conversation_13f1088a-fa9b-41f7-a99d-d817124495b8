import { EvaluationSession } from '@tess-f/sql-tables/dist/evaluations/session.js'
import { QuestionResponse } from '@tess-f/sql-tables/dist/evaluations/question-response.js'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

/**
 * Request payload for submitting a completed assessment
 * Uses composite type from SQL package interfaces
 */
export type SubmitAssessmentRequest = {
  /** The session ID for this assessment attempt */
  sessionId: EvaluationSession['Id']
  /** Array of all question responses */
  responses: QuestionResponse[]
  /** Assessment start time */
  startTime: EvaluationSession['Start']
  /** Assessment end time */
  endTime: EvaluationSession['End']
}

/**
 * Response payload after successful assessment submission
 * Uses composite type from SQL package interfaces
 */
export type SubmitAssessmentResponse =
  Required<Pick<EvaluationSession, 'Id' | 'Passed' | 'Score'>> &
  {
    /** Maximum possible score */
    maxScore: number
    /** Number of responses processed */
    responsesCount: number
    /** Number of questions that were graded */
    gradedQuestionsCount: number
    /** Number of questions pending manual grading */
    pendingQuestionsCount: number
  }

// Validation schema for QuestionResponse (removed Notes field as students don't send notes)
export const questionResponseSchema = z.object({
  SessionId: zodGUID.optional(),
  QuestionId: zodGUID,
  QuestionVersion: z.number().int().positive(),
  OptionId: zodGUID.optional(),
  OptionVersion: z.number().int().positive().optional(),
  TargetOptionId: zodGUID.optional(),
  TargetOptionVersion: z.number().int().positive().optional(),
  ResponseText: z.string().optional(),
  Duration: z.string().regex(/^PT(?:\d+H)?(?:\d+M)?(?:\d+(?:\.\d+)?S)?$/).optional(), // ISO 8601 duration
  OrderId: z.number().int().optional()
}).superRefine((data, ctx) => {
  // Validate option ID/version pairs
  if (data.OptionId && !data.OptionVersion) {
    ctx.addIssue({
      code: 'custom',
      message: 'OptionVersion is required when OptionId is provided',
      path: ['OptionVersion']
    })
  }
  if (data.TargetOptionId && !data.TargetOptionVersion) {
    ctx.addIssue({
      code: 'custom',
      message: 'TargetOptionVersion is required when TargetOptionId is provided',
      path: ['TargetOptionVersion']
    })
  }
})

// Validation schema for user submission (just an array of responses)
export const userSubmissionSchema = z.array(questionResponseSchema).min(1)

// Validation schema for SubmitAssessmentRequest (internal use)
export const submitAssessmentRequestSchema = z.object({
  sessionId: zodGUID,
  responses: z.array(questionResponseSchema).min(1),
  startTime: z.date(),
  endTime: z.date()
}).superRefine((data, ctx) => {
  // Validate that endTime is after startTime
  if (data.endTime <= data.startTime) {
    ctx.addIssue({
      code: 'custom',
      message: 'endTime must be after startTime',
      path: ['endTime']
    })
  }
})

// Validation schema for SubmitAssessmentResponse
export const submitAssessmentResponseSchema = z.object({
  Id: zodGUID,
  Passed: z.boolean(),
  Score: z.number().int().min(0),
  maxScore: z.number().int().min(0),
  responsesCount: z.number().int().min(0),
  gradedQuestionsCount: z.number().int().min(0),
  pendingQuestionsCount: z.number().int().min(0)
})

